<template>
  <div v-if="visible" class="custom-voice-dialog">
    <div class="custom-voice-dialog-content">
      <div class="card-decoration top-left"></div>
      <div class="card-decoration top-right"></div>
      <div class="card-decoration bottom-left"></div>
      <div class="card-decoration bottom-right"></div>
      
      <div class="dialog-header">
        <h3>定制新音色</h3>
        <button class="close-btn" @click="closeDialog">×</button>
      </div>
      <div class="dialog-body">
        <div class="voice-name-input" v-if="uploadStatus !== 'demo'" >
          <!-- <label for="voice-name">音色名称</label> -->
          <input type="text" id="voice-name" v-model="customVoiceName" placeholder="请输入音色名称" />
        </div>
        
        <div class="voice-gender-selection" v-if="uploadStatus !== 'demo'" >
          <!-- <label>音色性别</label> -->
          <div class="gender-options">
            <div class="gender-option" 
                 :class="{ active: voiceGender === 1 }"
                 @click="voiceGender = 1">
              <div class="gender-icon male"></div>
              <div class="gender-text">男声</div>
            </div>
            <div class="gender-option" 
                 :class="{ active: voiceGender === 2 }"
                 @click="voiceGender = 2">
              <div class="gender-icon female"></div>
              <div class="gender-text">女声</div>
            </div>
          </div>
        </div>
        
        <!-- 只在非试听状态下显示上传区域 -->
        <div v-if="uploadStatus !== 'demo'" class="voice-upload-area" @click="triggerFileUpload" :class="{'has-file': audioFile}">
          <input ref="fileInput" type="file" accept=".wav,.mp3,.m4a" style="display: none" @change="handleFileChange" />
          <div v-if="!audioFile" class="upload-placeholder">
            <div class="upload-icon">
              <span class="icon-upload"></span>
            </div>
            <div class="upload-text">点击上传音频文件</div>
            <div class="upload-hint">支持WAV格式，音频长度10秒-5分钟，大小不超过20MB</div>
          </div>
          <div v-else class="file-preview">
            <div class="file-info">
              <div class="file-name">{{audioFile.name}}</div>
              <div class="file-size">{{formatFileSize(audioFile.size)}}</div>
              <div class="file-duration" v-if="audioDuration">时长: {{formatDuration(audioDuration)}}</div>
            </div>
            <div class="file-actions">
              <button class="play-btn" @click.stop="togglePlayAudio" :class="{'playing': isAudioPlaying}">
                <span :class="isAudioPlaying ? 'icon-pause' : 'icon-play'"></span>
              </button>
              <button class="delete-btn" @click.stop="removeAudioFile">
                <span class="icon-delete"></span>
              </button>
            </div>
          </div>
        </div>
        
        <div class="upload-status" v-if="uploadStatus">
          <div class="status-progress" v-if="uploadStatus === 'uploading'">
            <!-- <div class="progress-bar">
              <div class="progress-fill" :style="{width: uploadProgress + '%'}"></div>
            </div> -->
            <div class="progress-text">上传克隆中</div>
          </div>
          <div class="status-error" v-else-if="uploadStatus === 'error'">
            <span class="icon-error"></span>
            <span>{{errorMessage}}</span>
          </div>
          <div class="status-demo" v-else-if="uploadStatus === 'demo'">
            <div class="demo-title">克隆完成，请试听样本音频：</div>

            <!-- 调试信息 -->
            <!-- <div class="debug-info" style="font-size: 12px; color: #666; margin-bottom: 10px;">
              <div>音频URL: {{ demoAudioUrl }}</div>
              <div>克隆记录ID: {{ cloneRecordId }}</div>
              <div>播放状态: {{ isDemoPlaying ? '播放中' : '未播放' }}</div>
            </div> -->

            <div class="demo-audio-controls">
              <!-- <button class="demo-play-btn" @click="playDemoAudio" :disabled="isDemoPlaying">
                <span v-if="isDemoPlaying" class="icon-pause"></span>
                <span v-else class="icon-play"></span>
                {{ isDemoPlaying ? '播放中...' : '试听样本' }}
              </button> -->
              <audio ref="demoAudioElement"
                     @ended="onDemoAudioEnded"
                     @loadstart="onDemoAudioLoadStart"
                     @canplay="onDemoAudioCanPlay"
                     @error="onDemoAudioError"
                     preload="auto"
                     controls
                     style="margin-top: 10px; width: 100%;"></audio>
            </div>
            <div class="demo-actions">
              <button class="demo-confirm-btn" @click="confirmClone" :disabled="isConfirming">
                {{ isConfirming ? '确认中...' : '确认使用此音色（1000积分）' }}
              </button>
              <button class="demo-cancel-btn" @click="cancelClone">重新上传</button>
            </div>
          </div>
        </div>
      </div>
      <div class="dialog-footer" v-if="uploadStatus !== 'demo'">
        <button class="cancel-btn" @click="closeDialog" v-if="uploadStatus !== 'uploading'">取消</button>
        <button class="submit-btn" @click="submitVoiceUpload" :disabled="!isFormValid || uploadStatus === 'uploading'">
          {{ uploadStatus === 'uploading' ? '克隆中...' : '开始定制' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeUnmount, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { uploadToOSS } from '@/api/oss.js';
import { cloneVoice, cloneVoiceConfirm } from '@/api/auth.js';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:visible', 'upload-success']);

// 自定义音色上传相关状态
const customVoiceName = ref('');
const voiceGender = ref(1); // 修改：音色性别，1为男性，2为女性，默认为1（男性）
const audioFile = ref(null);
const audioDuration = ref(null);
const fileInput = ref(null);
const uploadStatus = ref('demo'); // 'uploading', 'error', 'success'
const uploadProgress = ref(0);
const errorMessage = ref('');
const isAudioPlaying = ref(false);
const audioElement = ref(null);

// 试听相关状态
const demoAudioUrl = ref('https://wlpaas.weilitech.cn/dify/dev/1916418011341144064/audio/5af2a213c90d44a58c72d5797e6ad00b.wav');
const cloneRecordId = ref('3');
const isDemoPlaying = ref(false);
const demoAudioElement = ref(null);
const isConfirming = ref(false);

// 关闭上传对话框
const closeDialog = () => {
  emit('update:visible', false);
  stopAudio();
  resetForm();
};

// 重置表单
const resetForm = () => {
  customVoiceName.value = '';
  voiceGender.value = 1; // 重置性别选择
  removeAudioFile();
  uploadStatus.value = '';
  uploadProgress.value = 0;
  errorMessage.value = '';

  // 重置试听相关状态
  demoAudioUrl.value = '';
  cloneRecordId.value = '';
  isDemoPlaying.value = false;
  isConfirming.value = false;
  if (demoAudioElement.value) {
    demoAudioElement.value.pause();
    demoAudioElement.value.src = '';
  }
};

// 触发文件上传
const triggerFileUpload = () => {
  if (fileInput.value) {
    fileInput.value.click();
  }
};

// 处理文件选择
const handleFileChange = (event) => {
  const files = event.target.files;
  if (!files || files.length === 0) return;
  
  const file = files[0];
  
  // 检查文件类型
  // if (file.type !== 'audio/wav') {
  //   ElMessage.error('请上传WAV格式的音频文件');
  //   return;
  // }
  
  // 检查文件大小 (50MB限制)
  const maxSize = 50 * 1024 * 1024;
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过50MB');
    return;
  }
  
  // 保存文件并获取持续时间
  audioFile.value = file;
  
  // 创建音频元素来获取持续时间
  const audio = new Audio();
  audio.src = URL.createObjectURL(file);
  
  audio.onloadedmetadata = () => {
    audioDuration.value = audio.duration;
    
    // 检查音频时长是否在10秒-5分钟之间
    const minDuration = 10; // 10秒
    const maxDuration = 5 * 60; // 5分钟
    
    if (audio.duration < minDuration) {
      ElMessage.warning('音频时长应不少于10秒');
    } else if (audio.duration > maxDuration) {
      ElMessage.warning('音频时长应不超过5分钟');
    }
  };
  
  audio.onerror = () => {
    ElMessage.error('无法解析音频文件');
    audioFile.value = null;
  };
  
  // 保存引用以便播放
  audioElement.value = audio;
  
  // 重置文件输入，以便可以选择相同的文件
  event.target.value = '';
};

// 移除音频文件
const removeAudioFile = () => {
  if (audioFile.value) {
    stopAudio();
    if (audioElement.value) {
      URL.revokeObjectURL(audioElement.value.src);
    }
    audioFile.value = null;
    audioDuration.value = null;
    audioElement.value = null;
  }
};

// 停止音频
const stopAudio = () => {
  if (audioElement.value && isAudioPlaying.value) {
    audioElement.value.pause();
    audioElement.value.currentTime = 0;
    isAudioPlaying.value = false;
  }
};

// 切换音频播放
const togglePlayAudio = (event) => {
  event.preventDefault();
  event.stopPropagation();
  
  if (!audioElement.value) return;
  
  if (isAudioPlaying.value) {
    stopAudio();
  } else {
    playAudio();
  }
};

// 播放音频
const playAudio = () => {
  if (!audioElement.value) return;
  
  audioElement.value.play().then(() => {
    isAudioPlaying.value = true;
    
    // 添加事件监听器以检测播放结束
    audioElement.value.onended = () => {
      isAudioPlaying.value = false;
    };
  }).catch(error => {
    console.error('播放失败:', error);
    ElMessage.error('播放音频失败');
  });
};

// 格式化文件大小
const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  }
};

// 格式化音频时长
const formatDuration = (seconds) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = Math.floor(seconds % 60);
  return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
};

// 表单验证
const isFormValid = computed(() => {
  return customVoiceName.value.trim().length > 0 && 
         audioFile.value !== null && 
         audioDuration.value >= 10 &&
         audioDuration.value <= 300 &&
         audioFile.value.size <= 20 * 1024 * 1024 &&
         voiceGender.value !== ''; // 验证性别是否已选择
});

// 提交音色上传
const submitVoiceUpload = async () => {
  if (!isFormValid.value) return;
  
  // 停止正在播放的音频
  stopAudio();
  
  try {
    // 设置上传状态
    uploadStatus.value = 'uploading';
    uploadProgress.value = 0;
    
    // 上传到OSS
    const ossResult = await uploadToOSS(audioFile.value, null, (progress) => {
      uploadProgress.value = Math.floor(progress * 100);
    });
    
    if (!ossResult || !ossResult.url) {
      throw new Error('上传文件失败');
    }
    
    // 调用音色定制API
    const cloneResult = await cloneVoice(
      ossResult.url,
      customVoiceName.value,
      voiceGender.value
    );

    // 处理成功响应，获取试听音频和克隆记录ID
    if (cloneResult && cloneResult.data) {
      demoAudioUrl.value = cloneResult.data.demoAudio;
      cloneRecordId.value = cloneResult.data.cloneRecordId;

      // 切换到试听状态
      uploadStatus.value = 'demo';
      ElMessage.success('音色克隆完成，请试听样本音频');

      // 预加载试听音频
      if (demoAudioElement.value && demoAudioUrl.value) {
        demoAudioElement.value.src = demoAudioUrl.value;
        demoAudioElement.value.load();
      }
    } else {
      throw new Error('克隆响应数据格式错误');
    }
    
  } catch (error) {
    console.error('音色定制失败:', error);
    uploadStatus.value = 'error';
    errorMessage.value = error.message || '提交失败，请重试';
    ElMessage.error('音色定制失败: ' + errorMessage.value);
  }
};

// 播放试听音频
const playDemoAudio = async () => {
  if (!demoAudioElement.value || !demoAudioUrl.value) {
    ElMessage.error('试听音频不可用');
    return;
  }

  try {
    if (isDemoPlaying.value) {
      demoAudioElement.value.pause();
      isDemoPlaying.value = false;
    } else {
      // 确保音频源已设置
      if (demoAudioElement.value.src !== demoAudioUrl.value) {
        demoAudioElement.value.src = demoAudioUrl.value;
        await demoAudioElement.value.load();
      }

      await demoAudioElement.value.play();
      isDemoPlaying.value = true;
    }
  } catch (error) {
    console.error('播放试听音频失败:', error);
    ElMessage.error('播放失败，请重试: ' + error.message);
  }
};

// 试听音频播放结束
const onDemoAudioEnded = () => {
  console.log('试听音频播放结束');
  isDemoPlaying.value = false;
};

// 试听音频开始加载
const onDemoAudioLoadStart = () => {
  console.log('试听音频开始加载:', demoAudioUrl.value);
};

// 试听音频可以播放
const onDemoAudioCanPlay = () => {
  console.log('试听音频可以播放');
};

// 试听音频加载错误
const onDemoAudioError = (event) => {
  console.error('试听音频加载错误:', event);
  ElMessage.error('音频加载失败，请检查网络连接');
  isDemoPlaying.value = false;
};

// 确认克隆
const confirmClone = async () => {
  if (!cloneRecordId.value) {
    ElMessage.error('克隆记录ID不存在');
    return;
  }

  try {
    isConfirming.value = true;

    // 调用确认克隆API
    await cloneVoiceConfirm(cloneRecordId.value);

    // 处理成功响应
    uploadStatus.value = 'success';
    ElMessage.success('音色克隆确认成功！');

    // 通知父组件上传成功
    emit('upload-success', {
      voiceName: customVoiceName.value,
      voiceGender: voiceGender.value,
      cloneRecordId: cloneRecordId.value
    });

    // 关闭对话框
    setTimeout(() => {
      closeDialog();
    }, 1500);

  } catch (error) {
    console.error('确认克隆失败:', error);
    ElMessage.error('确认失败: ' + (error.message || '请重试'));
  } finally {
    isConfirming.value = false;
  }
};

// 取消克隆，重新上传
const cancelClone = () => {
  // 重置到初始状态
  uploadStatus.value = '';
  demoAudioUrl.value = '';
  cloneRecordId.value = '';
  isDemoPlaying.value = false;
  isConfirming.value = false;

  if (demoAudioElement.value) {
    demoAudioElement.value.pause();
    demoAudioElement.value.src = '';
  }

  ElMessage.info('已取消，请重新上传音频文件');
};

// 测试音频URL可访问性
const testAudioUrl = async (url) => {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    console.log('音频URL测试结果:', response.status, response.statusText);
    return response.ok;
  } catch (error) {
    console.error('音频URL测试失败:', error);
    return false;
  }
};

// 监听试听音频URL变化，自动设置音频源
watch(demoAudioUrl, async (newUrl) => {
  if (newUrl && demoAudioElement.value) {
    await nextTick();

    // 测试URL可访问性
    const isAccessible = await testAudioUrl(newUrl);
    console.log('音频URL可访问性:', isAccessible);

    demoAudioElement.value.src = newUrl;
    demoAudioElement.value.load();
    console.log('试听音频源已设置:', newUrl);
  }
});

// 组件卸载前清理事件监听
onBeforeUnmount(() => {
  // 停止定制音频播放
  stopAudio();

  // 停止试听音频播放
  if (demoAudioElement.value) {
    demoAudioElement.value.pause();
  }

  // 清理可能的资源
  if (audioElement.value) {
    URL.revokeObjectURL(audioElement.value.src);
  }
});
</script>

<style scoped>
/* 自定义音色上传对话框样式 */
.custom-voice-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(8px);
  /* background-color: rgba(0, 0, 0, 0.111); */
  /* animation: fade-in 0.3s ease-out; */
}

@keyframes fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.custom-voice-dialog-content {
  width: 550px;
  max-width: 90%;
  background: rgba(255, 255, 255, 0.928);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 24px;
  overflow: visible;
  display: flex;
  flex-direction: column;
  position: relative;
  transform: translateZ(0);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.232);
  /* box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.8),
    inset 0 0 0 1px rgba(255, 255, 255, 0.4); */
  /* transition: all 0.5s cubic-bezier(0.25, 1, 0.5, 1), background-color 0.3s, box-shadow 0.3s, transform 0.3s;
  animation: dialog-in 0.5s cubic-bezier(0.34, 1.56, 0.64, 1); */
}

.custom-voice-dialog-content:hover {
  box-shadow: 0 20px 40px rgba(99, 102, 241, 0.15),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.9),
    inset 0 0 0 1px rgba(255, 255, 255, 0.5);
  transform: translateY(-5px);
}

@keyframes dialog-in {
  0% {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  70% {
    opacity: 1;
    transform: translateY(-7px) scale(1.02);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

body.dark .custom-voice-dialog-content {
  background: rgba(0, 0, 0, 0.922);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.05),
    inset 0 0 0 1px rgba(255, 255, 255, 0.05);
}

body.dark .custom-voice-dialog-content:hover {
  box-shadow: 0 20px 40px rgba(99, 102, 241, 0.2),
    inset 0 1px 0 0 rgba(255, 255, 255, 0.1),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* 卡片装饰元素 */
.card-decoration {
  position: absolute;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.3), rgba(138, 92, 246, 0.1));
  filter: blur(20px);
  transition: all 0.6s ease, background 0.3s;
  pointer-events: none;
  z-index: 0;
  animation: decoration-in 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  animation-fill-mode: forwards;
}

@keyframes decoration-in {
  0% {
    opacity: 0;
    transform: scale(0.5) translateY(20px);
    filter: blur(30px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
    filter: blur(20px);
  }
}

body.dark .card-decoration {
  opacity: 0.6;
}

.top-left {
  top: -20px;
  left: -20px;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(99, 102, 241, 0.1));
  animation-delay: 0.1s;
}

body.dark .top-left {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(99, 102, 241, 0.08));
}

.top-right {
  top: -30px;
  right: -30px;
  width: 100px;
  height: 100px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.3), rgba(168, 85, 247, 0.1));
  animation-delay: 0.2s;
}

body.dark .top-right {
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.2), rgba(168, 85, 247, 0.08));
}

.bottom-left {
  bottom: -40px;
  left: -20px;
  width: 150px;
  height: 150px;
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.3), rgba(20, 184, 166, 0.1));
  animation-delay: 0.25s;
}

body.dark .bottom-left {
  background: linear-gradient(135deg, rgba(6, 182, 212, 0.2), rgba(20, 184, 166, 0.08));
}

.bottom-right {
  bottom: -30px;
  right: -30px;
  background: linear-gradient(135deg, rgba(20, 184, 166, 0.3), rgba(6, 182, 212, 0.1));
  animation-delay: 0.3s;
}

body.dark .bottom-right {
  background: linear-gradient(135deg, rgba(20, 184, 166, 0.2), rgba(6, 182, 212, 0.08));
}

.custom-voice-dialog-content:hover .card-decoration {
  transform: scale(1.2);
}

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e4e7ed;
  position: relative;
  z-index: 1;
}

body.dark .dialog-header {
  border-color: var(--border-color);
}

.dialog-header h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #303133;
  position: relative;
}

.dialog-header h3::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 40px;
  height: 3px;
  background: linear-gradient(90deg, #409eff, #67c23a);
  border-radius: 3px;
}

body.dark .dialog-header h3 {
  color: var(--text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: #909399;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  transition: all 0.3s;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 1;
}

.close-btn:hover {
  color: #f56c6c;
  background-color: rgba(245, 108, 108, 0.1);
  transform: rotate(-90deg);
}

body.dark .close-btn {
  color: var(--text-tertiary);
  background-color: rgba(255, 255, 255, 0.1);
}

body.dark .close-btn:hover {
  color: #f56c6c;
  background-color: rgba(245, 108, 108, 0.2);
}

.dialog-body {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 20px;
  position: relative;
  z-index: 1;
}

.voice-name-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.voice-name-input label, .voice-gender-selection label {
  font-size: 15px;
  color: #606266;
  font-weight: 500;
}

body.dark .voice-name-input label, 
body.dark .voice-gender-selection label {
  color: var(--text-secondary);
}

.voice-name-input input {
  height: 40px;
  padding: 0 16px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  font-size: 15px;
  color: #606266;
  transition: all 0.3s;
  background-color: rgba(255, 255, 255, 0.9);
}

.voice-name-input input:focus {
  outline: none;
  border-color: #409eff;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2);
}

body.dark .voice-name-input input {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-primary);
}

body.dark .voice-name-input input:focus {
  border-color: #409eff;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.3);
}

/* 性别选择样式 */
.voice-gender-selection {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.gender-options {
  display: flex;
  gap: 16px;
}

.gender-option {
  display: flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  background-color: #fff;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
}

body.dark .gender-option {
  background-color: var(--bg-secondary);
  border-color: var(--border-color);
}

.gender-option:hover {
  border-color: #a5b4fc;
  background-color: #f9faff;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
}

body.dark .gender-option:hover {
  border-color: #818cf8;
  background-color: var(--bg-hover);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.gender-option.active {
  border-color: #4f46e5;
  background-color: #f1f5ff;
  box-shadow: 0 4px 10px rgba(79, 70, 229, 0.15);
}

body.dark .gender-option.active {
  border-color: #6366f1;
  background-color: rgba(99, 102, 241, 0.1);
  box-shadow: 0 4px 10px rgba(99, 102, 241, 0.2);
}

.gender-icon {
  width: 24px;
  height: 24px;
  margin-right: 12px;
  position: relative;
}

.gender-icon.male::before {
  content: "♂";
  color: #409eff;
  font-size: 20px;
  font-weight: bold;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.gender-icon.female::before {
  content: "♀";
  color: #ff6b9a;
  font-size: 20px;
  font-weight: bold;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.gender-text {
  font-size: 15px;
  color: #606266;
  font-weight: 500;
}

.gender-option.active .gender-text {
  color: #4f46e5;
}

body.dark .gender-text {
  color: var(--text-primary);
}

body.dark .gender-option.active .gender-text {
  color: #818cf8;
}

/* 文件上传区域样式 */
.voice-upload-area {
  height: 140px;
  border: 1px dashed #c0c4cc;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: rgba(64, 158, 255, 0.02);
  position: relative;
  z-index: 1;
}

.voice-upload-area.has-file {
  height: auto;
  min-height: 120px;
  border-style: solid;
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
  padding: 16px;
}

body.dark .voice-upload-area {
  border-color: var(--border-color);
  background-color: rgba(64, 158, 255, 0.05);
}

body.dark .voice-upload-area.has-file {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
}

.voice-upload-area:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.05);
  transform: translateY(-3px);
  box-shadow: 0 6px 15px rgba(64, 158, 255, 0.1);
}

body.dark .voice-upload-area:hover {
  border-color: #409eff;
  background-color: rgba(64, 158, 255, 0.1);
  box-shadow: 0 6px 15px rgba(64, 158, 255, 0.15);
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.upload-icon {
  font-size: 36px;
  color: #409eff;
  margin-bottom: 2px;
}

.icon-upload::before {
  content: "↑";
  font-size: 24px;
  line-height: 1;
}

.upload-text {
  font-size: 16px;
  color: #606266;
  margin-bottom: 4px;
}

body.dark .upload-text {
  color: var(--text-primary);
}

.upload-hint {
  font-size: 12px;
  color: #909399;
  padding: 0 16px;
}

body.dark .upload-hint {
  color: var(--text-secondary);
}

.file-preview {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: #303133;
  font-size: 15px;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

body.dark .file-name {
  color: var(--text-primary);
}

.file-size, .file-duration {
  color: #909399;
  font-size: 13px;
  margin-bottom: 2px;
}

body.dark .file-size, 
body.dark .file-duration {
  color: var(--text-secondary);
}

.file-actions {
  display: flex;
  gap: 8px;
}

.play-btn, .delete-btn {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.play-btn {
  background-color: rgba(64, 158, 255, 0.8);
  color: white;
}

.play-btn:hover {
  background-color: #409eff;
  transform: scale(1.1);
}

.play-btn.playing {
  background-color: #67c23a;
}

.play-btn.playing:hover {
  background-color: #85ce61;
}

.delete-btn {
  background-color: rgba(245, 108, 108, 0.8);
  color: white;
}

.delete-btn:hover {
  background-color: #f56c6c;
  transform: scale(1.1);
}

.icon-play::before {
  content: "▶";
  font-size: 14px;
}

.icon-pause::before {
  content: "❚❚";
  font-size: 14px;
}

.icon-delete::before {
  content: "×";
  font-size: 20px;
}

.upload-status {
  margin-top: 8px;
}

.status-progress {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.progress-bar {
  height: 6px;
  background-color: #e4e7ed;
  border-radius: 3px;
  overflow: hidden;
}

body.dark .progress-bar {
  background-color: var(--border-color);
}

.progress-fill {
  height: 100%;
  background-color: #409eff;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #409eff;
  text-align: right;
}

.status-error {
  display: flex;
  align-items: center;
  gap: 6px;
  color: #f56c6c;
  font-size: 12px;
}

.icon-error::before {
  content: "✕";
  font-weight: bold;
}

/* 试听状态样式 */
.status-demo {
  padding: 20px;
  background-color: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 12px;
  text-align: center;
}

.demo-title {
  font-size: 16px;
  font-weight: 600;
  color: #0369a1;
  margin-bottom: 16px;
}

.demo-audio-controls {
  margin-bottom: 20px;
}

.demo-play-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #0ea5e9;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
}

.demo-play-btn:hover:not(:disabled) {
  background-color: #0284c7;
  transform: translateY(-2px);
}

.demo-play-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.demo-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
}

.demo-confirm-btn, .demo-cancel-btn {
  padding: 10px 20px;
  font-size: 14px;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  border: none;
}

.demo-confirm-btn {
  background-color: #10b981;
  color: white;
}

.demo-confirm-btn:hover:not(:disabled) {
  background-color: #059669;
  transform: translateY(-2px);
}

.demo-confirm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.demo-cancel-btn {
  background-color: #f3f4f6;
  color: #6b7280;
  border: 1px solid #d1d5db;
}

.demo-cancel-btn:hover {
  background-color: #e5e7eb;
  transform: translateY(-2px);
}

.icon-play::before {
  content: "▶";
}

.icon-pause::before {
  content: "⏸";
}

.dialog-footer {
  padding: 16px 24px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  position: relative;
  z-index: 1;
}

.cancel-btn, .submit-btn {
  padding: 10px 20px;
  font-size: 15px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
}

.cancel-btn {
  background-color: #f0f2f5;
  border: 1px solid #dcdfe6;
  color: #606266;
}

.cancel-btn:hover {
  background-color: #e6e8eb;
  transform: translateY(-2px);
}

body.dark .cancel-btn {
  background-color: var(--bg-tertiary);
  border-color: var(--border-color);
  color: var(--text-secondary);
}

body.dark .cancel-btn:hover {
  background-color: var(--bg-quaternary);
}

.submit-btn {
  background-color: #409eff;
  border: 1px solid #409eff;
  color: white;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.submit-btn:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
  transform: translateY(-2px);
  box-shadow: 0 6px 15px rgba(64, 158, 255, 0.3);
}

.submit-btn:disabled {
  background-color: #a0cfff;
  border-color: #a0cfff;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

body.dark .submit-btn {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

body.dark .submit-btn:hover {
  box-shadow: 0 6px 15px rgba(64, 158, 255, 0.4);
}

body.dark .submit-btn:disabled {
  background-color: rgba(64, 158, 255, 0.4);
  border-color: rgba(64, 158, 255, 0.4);
  box-shadow: none;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .custom-voice-dialog-content {
    width: 90%;
    border-radius: 16px;
  }
  
  .dialog-header {
    padding: 16px 20px;
  }
  
  .dialog-body {
    padding: 20px;
    gap: 16px;
  }
  
  .dialog-footer {
    padding: 12px 20px 20px;
  }
  
  .voice-upload-area {
    height: 150px;
  }
  
  .upload-icon {
    font-size: 30px;
  }
  
  .upload-text {
    font-size: 14px;
  }
  
  .card-decoration {
    width: 60px;
    height: 60px;
  }
  
  .top-right, .bottom-left, .bottom-right {
    width: 60px;
    height: 60px;
  }
}

@media (max-width: 480px) {
  .dialog-header h3 {
    font-size: 18px;
  }
  
  .voice-upload-area {
    height: 120px;
  }
  
  .upload-icon {
    font-size: 24px;
    margin-bottom: 6px;
  }
  
  .upload-text {
    font-size: 13px;
  }
  
  .upload-hint {
    font-size: 11px;
  }
  
  .file-preview {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .file-actions {
    align-self: flex-end;
  }
  
  .play-btn, .delete-btn {
    width: 32px;
    height: 32px;
  }
  
  .cancel-btn, .submit-btn {
    padding: 8px 16px;
    font-size: 14px;
  }
  
  .gender-options {
    flex-direction: column;
    gap: 8px;
  }
  
  .gender-option {
    padding: 8px 12px;
  }
  
  .card-decoration {
    width: 40px;
    height: 40px;
  }
  
  .top-right, .bottom-left, .bottom-right {
    width: 40px;
    height: 40px;
  }
}

/* 暗色主题下的试听样式 */
body.dark .status-demo {
  background-color: rgba(14, 165, 233, 0.1);
  border-color: rgba(14, 165, 233, 0.3);
}

body.dark .demo-title {
  color: #38bdf8;
}

body.dark .demo-cancel-btn {
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border-color: var(--border-color);
}

body.dark .demo-cancel-btn:hover {
  background-color: var(--bg-quaternary);
}
</style>